"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jotai";
exports.ids = ["vendor-chunks/jotai"];
exports.modules = {

/***/ "(ssr)/../../node_modules/jotai/esm/react.mjs":
/*!**********************************************!*\
  !*** ../../node_modules/jotai/esm/react.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   useAtom: () => (/* binding */ useAtom),\n/* harmony export */   useAtomValue: () => (/* binding */ useAtomValue),\n/* harmony export */   useSetAtom: () => (/* binding */ useSetAtom),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var jotai_vanilla__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jotai/vanilla */ \"(ssr)/../../node_modules/jotai/esm/vanilla.mjs\");\n/* harmony import */ var jotai_vanilla_internals__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jotai/vanilla/internals */ \"(ssr)/../../node_modules/jotai/esm/vanilla/internals.mjs\");\n/* __next_internal_client_entry_do_not_use__ Provider,useAtom,useAtomValue,useSetAtom,useStore auto */ \n\n\nconst StoreContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(void 0);\nfunction useStore(options) {\n    const store = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(StoreContext);\n    return (options == null ? void 0 : options.store) || store || (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_1__.getDefaultStore)();\n}\nfunction Provider({ children, store }) {\n    const storeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(void 0);\n    if (!store && !storeRef.current) {\n        storeRef.current = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_1__.createStore)();\n    }\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(StoreContext.Provider, {\n        value: store || storeRef.current\n    }, children);\n}\nconst isPromiseLike = (x)=>typeof (x == null ? void 0 : x.then) === \"function\";\nconst attachPromiseStatus = (promise)=>{\n    if (!promise.status) {\n        promise.status = \"pending\";\n        promise.then((v)=>{\n            promise.status = \"fulfilled\";\n            promise.value = v;\n        }, (e)=>{\n            promise.status = \"rejected\";\n            promise.reason = e;\n        });\n    }\n};\nconst use = react__WEBPACK_IMPORTED_MODULE_0__.use || // A shim for older React versions\n((promise)=>{\n    if (promise.status === \"pending\") {\n        throw promise;\n    } else if (promise.status === \"fulfilled\") {\n        return promise.value;\n    } else if (promise.status === \"rejected\") {\n        throw promise.reason;\n    } else {\n        attachPromiseStatus(promise);\n        throw promise;\n    }\n});\nconst continuablePromiseMap = /* @__PURE__ */ new WeakMap();\nconst createContinuablePromise = (promise, getValue)=>{\n    let continuablePromise = continuablePromiseMap.get(promise);\n    if (!continuablePromise) {\n        continuablePromise = new Promise((resolve, reject)=>{\n            let curr = promise;\n            const onFulfilled = (me)=>(v)=>{\n                    if (curr === me) {\n                        resolve(v);\n                    }\n                };\n            const onRejected = (me)=>(e)=>{\n                    if (curr === me) {\n                        reject(e);\n                    }\n                };\n            const onAbort = ()=>{\n                try {\n                    const nextValue = getValue();\n                    if (isPromiseLike(nextValue)) {\n                        continuablePromiseMap.set(nextValue, continuablePromise);\n                        curr = nextValue;\n                        nextValue.then(onFulfilled(nextValue), onRejected(nextValue));\n                        (0,jotai_vanilla_internals__WEBPACK_IMPORTED_MODULE_2__.INTERNAL_registerAbortHandler)(nextValue, onAbort);\n                    } else {\n                        resolve(nextValue);\n                    }\n                } catch (e) {\n                    reject(e);\n                }\n            };\n            promise.then(onFulfilled(promise), onRejected(promise));\n            (0,jotai_vanilla_internals__WEBPACK_IMPORTED_MODULE_2__.INTERNAL_registerAbortHandler)(promise, onAbort);\n        });\n        continuablePromiseMap.set(promise, continuablePromise);\n    }\n    return continuablePromise;\n};\nfunction useAtomValue(atom, options) {\n    const { delay, unstable_promiseStatus: promiseStatus = !react__WEBPACK_IMPORTED_MODULE_0__.use } = options || {};\n    const store = useStore(options);\n    const [[valueFromReducer, storeFromReducer, atomFromReducer], rerender] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)({\n        \"useAtomValue.useReducer\": (prev)=>{\n            const nextValue = store.get(atom);\n            if (Object.is(prev[0], nextValue) && prev[1] === store && prev[2] === atom) {\n                return prev;\n            }\n            return [\n                nextValue,\n                store,\n                atom\n            ];\n        }\n    }[\"useAtomValue.useReducer\"], void 0, {\n        \"useAtomValue.useReducer\": ()=>[\n                store.get(atom),\n                store,\n                atom\n            ]\n    }[\"useAtomValue.useReducer\"]);\n    let value = valueFromReducer;\n    if (storeFromReducer !== store || atomFromReducer !== atom) {\n        rerender();\n        value = store.get(atom);\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAtomValue.useEffect\": ()=>{\n            const unsub = store.sub(atom, {\n                \"useAtomValue.useEffect.unsub\": ()=>{\n                    if (promiseStatus) {\n                        try {\n                            const value2 = store.get(atom);\n                            if (isPromiseLike(value2)) {\n                                attachPromiseStatus(createContinuablePromise(value2, {\n                                    \"useAtomValue.useEffect.unsub\": ()=>store.get(atom)\n                                }[\"useAtomValue.useEffect.unsub\"]));\n                            }\n                        } catch (e) {}\n                    }\n                    if (typeof delay === \"number\") {\n                        setTimeout(rerender, delay);\n                        return;\n                    }\n                    rerender();\n                }\n            }[\"useAtomValue.useEffect.unsub\"]);\n            rerender();\n            return unsub;\n        }\n    }[\"useAtomValue.useEffect\"], [\n        store,\n        atom,\n        delay,\n        promiseStatus\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue)(value);\n    if (isPromiseLike(value)) {\n        const promise = createContinuablePromise(value, ()=>store.get(atom));\n        if (promiseStatus) {\n            attachPromiseStatus(promise);\n        }\n        return use(promise);\n    }\n    return value;\n}\nfunction useSetAtom(atom, options) {\n    const store = useStore(options);\n    const setAtom = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSetAtom.useCallback[setAtom]\": (...args)=>{\n            if (( false ? 0 : void 0) !== \"production\" && !(\"write\" in atom)) {\n                throw new Error(\"not writable atom\");\n            }\n            return store.set(atom, ...args);\n        }\n    }[\"useSetAtom.useCallback[setAtom]\"], [\n        store,\n        atom\n    ]);\n    return setAtom;\n}\nfunction useAtom(atom, options) {\n    return [\n        useAtomValue(atom, options),\n        // We do wrong type assertion here, which results in throwing an error.\n        useSetAtom(atom, options)\n    ];\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/jotai/esm/react.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/jotai/esm/vanilla.mjs":
/*!************************************************!*\
  !*** ../../node_modules/jotai/esm/vanilla.mjs ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INTERNAL_overrideCreateStore: () => (/* binding */ INTERNAL_overrideCreateStore),\n/* harmony export */   atom: () => (/* binding */ atom),\n/* harmony export */   createStore: () => (/* binding */ createStore),\n/* harmony export */   getDefaultStore: () => (/* binding */ getDefaultStore)\n/* harmony export */ });\n/* harmony import */ var jotai_vanilla_internals__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jotai/vanilla/internals */ \"(ssr)/../../node_modules/jotai/esm/vanilla/internals.mjs\");\n\n\nlet keyCount = 0;\nfunction atom(read, write) {\n  const key = `atom${++keyCount}`;\n  const config = {\n    toString() {\n      return ( false ? 0 : void 0) !== \"production\" && this.debugLabel ? key + \":\" + this.debugLabel : key;\n    }\n  };\n  if (typeof read === \"function\") {\n    config.read = read;\n  } else {\n    config.init = read;\n    config.read = defaultRead;\n    config.write = defaultWrite;\n  }\n  if (write) {\n    config.write = write;\n  }\n  return config;\n}\nfunction defaultRead(get) {\n  return get(this);\n}\nfunction defaultWrite(get, set, arg) {\n  return set(\n    this,\n    typeof arg === \"function\" ? arg(get(this)) : arg\n  );\n}\n\nconst createDevStoreRev4 = () => {\n  let inRestoreAtom = 0;\n  const storeHooks = (0,jotai_vanilla_internals__WEBPACK_IMPORTED_MODULE_0__.INTERNAL_initializeStoreHooks)({});\n  const atomStateMap = /* @__PURE__ */ new WeakMap();\n  const mountedAtoms = /* @__PURE__ */ new WeakMap();\n  const store = (0,jotai_vanilla_internals__WEBPACK_IMPORTED_MODULE_0__.INTERNAL_buildStoreRev1)(\n    atomStateMap,\n    mountedAtoms,\n    void 0,\n    void 0,\n    void 0,\n    void 0,\n    storeHooks,\n    void 0,\n    (atom, get, set, ...args) => {\n      if (inRestoreAtom) {\n        return set(atom, ...args);\n      }\n      return atom.write(get, set, ...args);\n    }\n  );\n  const debugMountedAtoms = /* @__PURE__ */ new Set();\n  storeHooks.m.add(void 0, (atom) => {\n    debugMountedAtoms.add(atom);\n    const atomState = atomStateMap.get(atom);\n    atomState.m = mountedAtoms.get(atom);\n  });\n  storeHooks.u.add(void 0, (atom) => {\n    debugMountedAtoms.delete(atom);\n    const atomState = atomStateMap.get(atom);\n    delete atomState.m;\n  });\n  const devStore = {\n    // store dev methods (these are tentative and subject to change without notice)\n    dev4_get_internal_weak_map: () => {\n      console.log(\"Deprecated: Use devstore from the devtools library\");\n      return atomStateMap;\n    },\n    dev4_get_mounted_atoms: () => debugMountedAtoms,\n    dev4_restore_atoms: (values) => {\n      const restoreAtom = {\n        read: () => null,\n        write: (_get, set) => {\n          ++inRestoreAtom;\n          try {\n            for (const [atom, value] of values) {\n              if (\"init\" in atom) {\n                set(atom, value);\n              }\n            }\n          } finally {\n            --inRestoreAtom;\n          }\n        }\n      };\n      store.set(restoreAtom);\n    }\n  };\n  return Object.assign(store, devStore);\n};\nlet overiddenCreateStore;\nfunction INTERNAL_overrideCreateStore(fn) {\n  overiddenCreateStore = fn(overiddenCreateStore);\n}\nfunction createStore() {\n  if (overiddenCreateStore) {\n    return overiddenCreateStore();\n  }\n  if (( false ? 0 : void 0) !== \"production\") {\n    return createDevStoreRev4();\n  }\n  return (0,jotai_vanilla_internals__WEBPACK_IMPORTED_MODULE_0__.INTERNAL_buildStoreRev1)();\n}\nlet defaultStore;\nfunction getDefaultStore() {\n  if (!defaultStore) {\n    defaultStore = createStore();\n    if (( false ? 0 : void 0) !== \"production\") {\n      globalThis.__JOTAI_DEFAULT_STORE__ || (globalThis.__JOTAI_DEFAULT_STORE__ = defaultStore);\n      if (globalThis.__JOTAI_DEFAULT_STORE__ !== defaultStore) {\n        console.warn(\n          \"Detected multiple Jotai instances. It may cause unexpected behavior with the default store. https://github.com/pmndrs/jotai/discussions/2044\"\n        );\n      }\n    }\n  }\n  return defaultStore;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/jotai/esm/vanilla.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/jotai/esm/vanilla/internals.mjs":
/*!**********************************************************!*\
  !*** ../../node_modules/jotai/esm/vanilla/internals.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INTERNAL_abortPromise: () => (/* binding */ INTERNAL_abortPromise),\n/* harmony export */   INTERNAL_addPendingPromiseToDependency: () => (/* binding */ INTERNAL_addPendingPromiseToDependency),\n/* harmony export */   INTERNAL_buildStoreRev1: () => (/* binding */ INTERNAL_buildStoreRev1),\n/* harmony export */   INTERNAL_getBuildingBlocksRev1: () => (/* binding */ INTERNAL_getBuildingBlocksRev1),\n/* harmony export */   INTERNAL_getMountedOrPendingDependents: () => (/* binding */ INTERNAL_getMountedOrPendingDependents),\n/* harmony export */   INTERNAL_hasInitialValue: () => (/* binding */ INTERNAL_hasInitialValue),\n/* harmony export */   INTERNAL_initializeStoreHooks: () => (/* binding */ INTERNAL_initializeStoreHooks),\n/* harmony export */   INTERNAL_isActuallyWritableAtom: () => (/* binding */ INTERNAL_isActuallyWritableAtom),\n/* harmony export */   INTERNAL_isAtomStateInitialized: () => (/* binding */ INTERNAL_isAtomStateInitialized),\n/* harmony export */   INTERNAL_isPendingPromise: () => (/* binding */ INTERNAL_isPendingPromise),\n/* harmony export */   INTERNAL_isPromiseLike: () => (/* binding */ INTERNAL_isPromiseLike),\n/* harmony export */   INTERNAL_isSelfAtom: () => (/* binding */ INTERNAL_isSelfAtom),\n/* harmony export */   INTERNAL_promiseStateMap: () => (/* binding */ INTERNAL_promiseStateMap),\n/* harmony export */   INTERNAL_registerAbortHandler: () => (/* binding */ INTERNAL_registerAbortHandler),\n/* harmony export */   INTERNAL_returnAtomValue: () => (/* binding */ INTERNAL_returnAtomValue),\n/* harmony export */   INTERNAL_setAtomStateValueOrPromise: () => (/* binding */ INTERNAL_setAtomStateValueOrPromise)\n/* harmony export */ });\nconst isSelfAtom = (atom, a) => atom.unstable_is ? atom.unstable_is(a) : a === atom;\nconst hasInitialValue = (atom) => \"init\" in atom;\nconst isActuallyWritableAtom = (atom) => !!atom.write;\nconst isAtomStateInitialized = (atomState) => \"v\" in atomState || \"e\" in atomState;\nconst returnAtomValue = (atomState) => {\n  if (\"e\" in atomState) {\n    throw atomState.e;\n  }\n  if (( false ? 0 : void 0) !== \"production\" && !(\"v\" in atomState)) {\n    throw new Error(\"[Bug] atom state is not initialized\");\n  }\n  return atomState.v;\n};\nconst promiseStateMap = /* @__PURE__ */ new WeakMap();\nconst isPendingPromise = (value) => {\n  var _a;\n  return isPromiseLike(value) && !!((_a = promiseStateMap.get(value)) == null ? void 0 : _a[0]);\n};\nconst abortPromise = (promise) => {\n  const promiseState = promiseStateMap.get(promise);\n  if (promiseState == null ? void 0 : promiseState[0]) {\n    promiseState[0] = false;\n    promiseState[1].forEach((fn) => fn());\n  }\n};\nconst registerAbortHandler = (promise, abortHandler) => {\n  let promiseState = promiseStateMap.get(promise);\n  if (!promiseState) {\n    promiseState = [true, /* @__PURE__ */ new Set()];\n    promiseStateMap.set(promise, promiseState);\n    const settle = () => {\n      promiseState[0] = false;\n    };\n    promise.then(settle, settle);\n  }\n  promiseState[1].add(abortHandler);\n};\nconst isPromiseLike = (p) => typeof (p == null ? void 0 : p.then) === \"function\";\nconst addPendingPromiseToDependency = (atom, promise, dependencyAtomState) => {\n  if (!dependencyAtomState.p.has(atom)) {\n    dependencyAtomState.p.add(atom);\n    promise.then(\n      () => {\n        dependencyAtomState.p.delete(atom);\n      },\n      () => {\n        dependencyAtomState.p.delete(atom);\n      }\n    );\n  }\n};\nconst setAtomStateValueOrPromise = (atom, valueOrPromise, ensureAtomState) => {\n  const atomState = ensureAtomState(atom);\n  const hasPrevValue = \"v\" in atomState;\n  const prevValue = atomState.v;\n  if (isPromiseLike(valueOrPromise)) {\n    for (const a of atomState.d.keys()) {\n      addPendingPromiseToDependency(atom, valueOrPromise, ensureAtomState(a));\n    }\n  }\n  atomState.v = valueOrPromise;\n  delete atomState.e;\n  if (!hasPrevValue || !Object.is(prevValue, atomState.v)) {\n    ++atomState.n;\n    if (isPromiseLike(prevValue)) {\n      abortPromise(prevValue);\n    }\n  }\n};\nconst getMountedOrPendingDependents = (atom, atomState, mountedMap) => {\n  var _a;\n  const dependents = /* @__PURE__ */ new Set();\n  for (const a of ((_a = mountedMap.get(atom)) == null ? void 0 : _a.t) || []) {\n    if (mountedMap.has(a)) {\n      dependents.add(a);\n    }\n  }\n  for (const atomWithPendingPromise of atomState.p) {\n    dependents.add(atomWithPendingPromise);\n  }\n  return dependents;\n};\nconst createStoreHook = () => {\n  const callbacks = /* @__PURE__ */ new Set();\n  const notify = () => {\n    callbacks.forEach((fn) => fn());\n  };\n  notify.add = (fn) => {\n    callbacks.add(fn);\n    return () => {\n      callbacks.delete(fn);\n    };\n  };\n  return notify;\n};\nconst createStoreHookForAtoms = () => {\n  const all = {};\n  const callbacks = /* @__PURE__ */ new WeakMap();\n  const notify = (atom) => {\n    var _a, _b;\n    (_a = callbacks.get(all)) == null ? void 0 : _a.forEach((fn) => fn(atom));\n    (_b = callbacks.get(atom)) == null ? void 0 : _b.forEach((fn) => fn());\n  };\n  notify.add = (atom, fn) => {\n    const key = atom || all;\n    const fns = (callbacks.has(key) ? callbacks : callbacks.set(key, /* @__PURE__ */ new Set())).get(key);\n    fns.add(fn);\n    return () => {\n      fns == null ? void 0 : fns.delete(fn);\n      if (!fns.size) {\n        callbacks.delete(key);\n      }\n    };\n  };\n  return notify;\n};\nconst initializeStoreHooks = (storeHooks) => {\n  storeHooks.c || (storeHooks.c = createStoreHookForAtoms());\n  storeHooks.m || (storeHooks.m = createStoreHookForAtoms());\n  storeHooks.u || (storeHooks.u = createStoreHookForAtoms());\n  storeHooks.f || (storeHooks.f = createStoreHook());\n  return storeHooks;\n};\nconst BUILDING_BLOCKS = Symbol();\nconst getBuildingBlocks = (store) => store[BUILDING_BLOCKS];\nconst buildStore = (atomStateMap = /* @__PURE__ */ new WeakMap(), mountedMap = /* @__PURE__ */ new WeakMap(), invalidatedAtoms = /* @__PURE__ */ new WeakMap(), changedAtoms = /* @__PURE__ */ new Set(), mountCallbacks = /* @__PURE__ */ new Set(), unmountCallbacks = /* @__PURE__ */ new Set(), storeHooks = {}, atomRead = (atom, ...params) => atom.read(...params), atomWrite = (atom, ...params) => atom.write(...params), atomOnInit = (atom, store) => {\n  var _a;\n  return (_a = atom.unstable_onInit) == null ? void 0 : _a.call(atom, store);\n}, atomOnMount = (atom, setAtom) => {\n  var _a;\n  return (_a = atom.onMount) == null ? void 0 : _a.call(atom, setAtom);\n}, ...buildingBlockFunctions) => {\n  const ensureAtomState = buildingBlockFunctions[0] || ((atom) => {\n    if (( false ? 0 : void 0) !== \"production\" && !atom) {\n      throw new Error(\"Atom is undefined or null\");\n    }\n    let atomState = atomStateMap.get(atom);\n    if (!atomState) {\n      atomState = { d: /* @__PURE__ */ new Map(), p: /* @__PURE__ */ new Set(), n: 0 };\n      atomStateMap.set(atom, atomState);\n      atomOnInit == null ? void 0 : atomOnInit(atom, store);\n    }\n    return atomState;\n  });\n  const flushCallbacks = buildingBlockFunctions[1] || (() => {\n    const errors = [];\n    const call = (fn) => {\n      try {\n        fn();\n      } catch (e) {\n        errors.push(e);\n      }\n    };\n    do {\n      if (storeHooks.f) {\n        call(storeHooks.f);\n      }\n      const callbacks = /* @__PURE__ */ new Set();\n      const add = callbacks.add.bind(callbacks);\n      changedAtoms.forEach((atom) => {\n        var _a;\n        return (_a = mountedMap.get(atom)) == null ? void 0 : _a.l.forEach(add);\n      });\n      changedAtoms.clear();\n      unmountCallbacks.forEach(add);\n      unmountCallbacks.clear();\n      mountCallbacks.forEach(add);\n      mountCallbacks.clear();\n      callbacks.forEach(call);\n      if (changedAtoms.size) {\n        recomputeInvalidatedAtoms();\n      }\n    } while (changedAtoms.size || unmountCallbacks.size || mountCallbacks.size);\n    if (errors.length) {\n      throw new AggregateError(errors);\n    }\n  });\n  const recomputeInvalidatedAtoms = buildingBlockFunctions[2] || (() => {\n    const topSortedReversed = [];\n    const visiting = /* @__PURE__ */ new WeakSet();\n    const visited = /* @__PURE__ */ new WeakSet();\n    const stack = Array.from(changedAtoms);\n    while (stack.length) {\n      const a = stack[stack.length - 1];\n      const aState = ensureAtomState(a);\n      if (visited.has(a)) {\n        stack.pop();\n        continue;\n      }\n      if (visiting.has(a)) {\n        if (invalidatedAtoms.get(a) === aState.n) {\n          topSortedReversed.push([a, aState]);\n        } else if (( false ? 0 : void 0) !== \"production\" && invalidatedAtoms.has(a)) {\n          throw new Error(\"[Bug] invalidated atom exists\");\n        }\n        visited.add(a);\n        stack.pop();\n        continue;\n      }\n      visiting.add(a);\n      for (const d of getMountedOrPendingDependents(a, aState, mountedMap)) {\n        if (!visiting.has(d)) {\n          stack.push(d);\n        }\n      }\n    }\n    for (let i = topSortedReversed.length - 1; i >= 0; --i) {\n      const [a, aState] = topSortedReversed[i];\n      let hasChangedDeps = false;\n      for (const dep of aState.d.keys()) {\n        if (dep !== a && changedAtoms.has(dep)) {\n          hasChangedDeps = true;\n          break;\n        }\n      }\n      if (hasChangedDeps) {\n        readAtomState(a);\n        mountDependencies(a);\n      }\n      invalidatedAtoms.delete(a);\n    }\n  });\n  const readAtomState = buildingBlockFunctions[3] || ((atom) => {\n    var _a;\n    const atomState = ensureAtomState(atom);\n    if (isAtomStateInitialized(atomState)) {\n      if (mountedMap.has(atom) && invalidatedAtoms.get(atom) !== atomState.n) {\n        return atomState;\n      }\n      if (Array.from(atomState.d).every(\n        ([a, n]) => (\n          // Recursively, read the atom state of the dependency, and\n          // check if the atom epoch number is unchanged\n          readAtomState(a).n === n\n        )\n      )) {\n        return atomState;\n      }\n    }\n    atomState.d.clear();\n    let isSync = true;\n    const mountDependenciesIfAsync = () => {\n      if (mountedMap.has(atom)) {\n        mountDependencies(atom);\n        recomputeInvalidatedAtoms();\n        flushCallbacks();\n      }\n    };\n    const getter = (a) => {\n      var _a2;\n      if (isSelfAtom(atom, a)) {\n        const aState2 = ensureAtomState(a);\n        if (!isAtomStateInitialized(aState2)) {\n          if (hasInitialValue(a)) {\n            setAtomStateValueOrPromise(a, a.init, ensureAtomState);\n          } else {\n            throw new Error(\"no atom init\");\n          }\n        }\n        return returnAtomValue(aState2);\n      }\n      const aState = readAtomState(a);\n      try {\n        return returnAtomValue(aState);\n      } finally {\n        atomState.d.set(a, aState.n);\n        if (isPendingPromise(atomState.v)) {\n          addPendingPromiseToDependency(atom, atomState.v, aState);\n        }\n        (_a2 = mountedMap.get(a)) == null ? void 0 : _a2.t.add(atom);\n        if (!isSync) {\n          mountDependenciesIfAsync();\n        }\n      }\n    };\n    let controller;\n    let setSelf;\n    const options = {\n      get signal() {\n        if (!controller) {\n          controller = new AbortController();\n        }\n        return controller.signal;\n      },\n      get setSelf() {\n        if (( false ? 0 : void 0) !== \"production\" && !isActuallyWritableAtom(atom)) {\n          console.warn(\"setSelf function cannot be used with read-only atom\");\n        }\n        if (!setSelf && isActuallyWritableAtom(atom)) {\n          setSelf = (...args) => {\n            if (( false ? 0 : void 0) !== \"production\" && isSync) {\n              console.warn(\"setSelf function cannot be called in sync\");\n            }\n            if (!isSync) {\n              try {\n                return writeAtomState(atom, ...args);\n              } finally {\n                recomputeInvalidatedAtoms();\n                flushCallbacks();\n              }\n            }\n          };\n        }\n        return setSelf;\n      }\n    };\n    const prevEpochNumber = atomState.n;\n    try {\n      const valueOrPromise = atomRead(atom, getter, options);\n      setAtomStateValueOrPromise(atom, valueOrPromise, ensureAtomState);\n      if (isPromiseLike(valueOrPromise)) {\n        registerAbortHandler(valueOrPromise, () => controller == null ? void 0 : controller.abort());\n        valueOrPromise.then(\n          mountDependenciesIfAsync,\n          mountDependenciesIfAsync\n        );\n      }\n      return atomState;\n    } catch (error) {\n      delete atomState.v;\n      atomState.e = error;\n      ++atomState.n;\n      return atomState;\n    } finally {\n      isSync = false;\n      if (prevEpochNumber !== atomState.n && invalidatedAtoms.get(atom) === prevEpochNumber) {\n        invalidatedAtoms.set(atom, atomState.n);\n        changedAtoms.add(atom);\n        (_a = storeHooks.c) == null ? void 0 : _a.call(storeHooks, atom);\n      }\n    }\n  });\n  const invalidateDependents = buildingBlockFunctions[4] || ((atom) => {\n    const stack = [atom];\n    while (stack.length) {\n      const a = stack.pop();\n      const aState = ensureAtomState(a);\n      for (const d of getMountedOrPendingDependents(a, aState, mountedMap)) {\n        const dState = ensureAtomState(d);\n        invalidatedAtoms.set(d, dState.n);\n        stack.push(d);\n      }\n    }\n  });\n  const writeAtomState = buildingBlockFunctions[5] || ((atom, ...args) => {\n    let isSync = true;\n    const getter = (a) => returnAtomValue(readAtomState(a));\n    const setter = (a, ...args2) => {\n      var _a;\n      const aState = ensureAtomState(a);\n      try {\n        if (isSelfAtom(atom, a)) {\n          if (!hasInitialValue(a)) {\n            throw new Error(\"atom not writable\");\n          }\n          const prevEpochNumber = aState.n;\n          const v = args2[0];\n          setAtomStateValueOrPromise(a, v, ensureAtomState);\n          mountDependencies(a);\n          if (prevEpochNumber !== aState.n) {\n            changedAtoms.add(a);\n            (_a = storeHooks.c) == null ? void 0 : _a.call(storeHooks, a);\n            invalidateDependents(a);\n          }\n          return void 0;\n        } else {\n          return writeAtomState(a, ...args2);\n        }\n      } finally {\n        if (!isSync) {\n          recomputeInvalidatedAtoms();\n          flushCallbacks();\n        }\n      }\n    };\n    try {\n      return atomWrite(atom, getter, setter, ...args);\n    } finally {\n      isSync = false;\n    }\n  });\n  const mountDependencies = buildingBlockFunctions[6] || ((atom) => {\n    var _a;\n    const atomState = ensureAtomState(atom);\n    const mounted = mountedMap.get(atom);\n    if (mounted && !isPendingPromise(atomState.v)) {\n      for (const [a, n] of atomState.d) {\n        if (!mounted.d.has(a)) {\n          const aState = ensureAtomState(a);\n          const aMounted = mountAtom(a);\n          aMounted.t.add(atom);\n          mounted.d.add(a);\n          if (n !== aState.n) {\n            changedAtoms.add(a);\n            (_a = storeHooks.c) == null ? void 0 : _a.call(storeHooks, a);\n            invalidateDependents(a);\n          }\n        }\n      }\n      for (const a of mounted.d || []) {\n        if (!atomState.d.has(a)) {\n          mounted.d.delete(a);\n          const aMounted = unmountAtom(a);\n          aMounted == null ? void 0 : aMounted.t.delete(atom);\n        }\n      }\n    }\n  });\n  const mountAtom = buildingBlockFunctions[7] || ((atom) => {\n    var _a;\n    const atomState = ensureAtomState(atom);\n    let mounted = mountedMap.get(atom);\n    if (!mounted) {\n      readAtomState(atom);\n      for (const a of atomState.d.keys()) {\n        const aMounted = mountAtom(a);\n        aMounted.t.add(atom);\n      }\n      mounted = {\n        l: /* @__PURE__ */ new Set(),\n        d: new Set(atomState.d.keys()),\n        t: /* @__PURE__ */ new Set()\n      };\n      mountedMap.set(atom, mounted);\n      (_a = storeHooks.m) == null ? void 0 : _a.call(storeHooks, atom);\n      if (isActuallyWritableAtom(atom)) {\n        const processOnMount = () => {\n          let isSync = true;\n          const setAtom = (...args) => {\n            try {\n              return writeAtomState(atom, ...args);\n            } finally {\n              if (!isSync) {\n                recomputeInvalidatedAtoms();\n                flushCallbacks();\n              }\n            }\n          };\n          try {\n            const onUnmount = atomOnMount(atom, setAtom);\n            if (onUnmount) {\n              mounted.u = () => {\n                isSync = true;\n                try {\n                  onUnmount();\n                } finally {\n                  isSync = false;\n                }\n              };\n            }\n          } finally {\n            isSync = false;\n          }\n        };\n        mountCallbacks.add(processOnMount);\n      }\n    }\n    return mounted;\n  });\n  const unmountAtom = buildingBlockFunctions[8] || ((atom) => {\n    var _a;\n    const atomState = ensureAtomState(atom);\n    let mounted = mountedMap.get(atom);\n    if (mounted && !mounted.l.size && !Array.from(mounted.t).some((a) => {\n      var _a2;\n      return (_a2 = mountedMap.get(a)) == null ? void 0 : _a2.d.has(atom);\n    })) {\n      if (mounted.u) {\n        unmountCallbacks.add(mounted.u);\n      }\n      mounted = void 0;\n      mountedMap.delete(atom);\n      (_a = storeHooks.u) == null ? void 0 : _a.call(storeHooks, atom);\n      for (const a of atomState.d.keys()) {\n        const aMounted = unmountAtom(a);\n        aMounted == null ? void 0 : aMounted.t.delete(atom);\n      }\n      return void 0;\n    }\n    return mounted;\n  });\n  const buildingBlocks = [\n    // store state\n    atomStateMap,\n    mountedMap,\n    invalidatedAtoms,\n    changedAtoms,\n    mountCallbacks,\n    unmountCallbacks,\n    storeHooks,\n    // atom interceptors\n    atomRead,\n    atomWrite,\n    atomOnInit,\n    atomOnMount,\n    // building-block functions\n    ensureAtomState,\n    flushCallbacks,\n    recomputeInvalidatedAtoms,\n    readAtomState,\n    invalidateDependents,\n    writeAtomState,\n    mountDependencies,\n    mountAtom,\n    unmountAtom\n  ];\n  const store = {\n    get: (atom) => returnAtomValue(readAtomState(atom)),\n    set: (atom, ...args) => {\n      try {\n        return writeAtomState(atom, ...args);\n      } finally {\n        recomputeInvalidatedAtoms();\n        flushCallbacks();\n      }\n    },\n    sub: (atom, listener) => {\n      const mounted = mountAtom(atom);\n      const listeners = mounted.l;\n      listeners.add(listener);\n      flushCallbacks();\n      return () => {\n        listeners.delete(listener);\n        unmountAtom(atom);\n        flushCallbacks();\n      };\n    }\n  };\n  Object.defineProperty(store, BUILDING_BLOCKS, { value: buildingBlocks });\n  return store;\n};\nconst INTERNAL_buildStoreRev1 = buildStore;\nconst INTERNAL_getBuildingBlocksRev1 = getBuildingBlocks;\nconst INTERNAL_initializeStoreHooks = initializeStoreHooks;\nconst INTERNAL_isSelfAtom = isSelfAtom;\nconst INTERNAL_hasInitialValue = hasInitialValue;\nconst INTERNAL_isActuallyWritableAtom = isActuallyWritableAtom;\nconst INTERNAL_isAtomStateInitialized = isAtomStateInitialized;\nconst INTERNAL_returnAtomValue = returnAtomValue;\nconst INTERNAL_promiseStateMap = promiseStateMap;\nconst INTERNAL_isPendingPromise = isPendingPromise;\nconst INTERNAL_abortPromise = abortPromise;\nconst INTERNAL_registerAbortHandler = registerAbortHandler;\nconst INTERNAL_isPromiseLike = isPromiseLike;\nconst INTERNAL_addPendingPromiseToDependency = addPendingPromiseToDependency;\nconst INTERNAL_setAtomStateValueOrPromise = setAtomStateValueOrPromise;\nconst INTERNAL_getMountedOrPendingDependents = getMountedOrPendingDependents;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/jotai/esm/vanilla/internals.mjs\n");

/***/ })

};
;
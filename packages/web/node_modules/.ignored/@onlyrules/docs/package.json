{"name": "@onlyrules/docs", "version": "0.1.0", "private": true, "main": "./lib/index.ts", "types": "./lib/index.ts", "exports": {".": "./lib/index.ts", "./source": "./lib/source.ts", "./utils": "./lib/utils.ts"}, "scripts": {"build": "echo 'Docs package - content processed by web package'", "type-check": "tsc --noEmit"}, "dependencies": {"fumadocs-core": "^15.3.0", "fumadocs-mdx": "^11.7.1", "fumadocs-ui": "^15.3.0", "@onlyrules/shared": "file:../shared", "next": "^15.3.5", "react": "18.2.0", "react-dom": "18.2.0", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "@tailwindcss/typography": "^0.5.14", "autoprefixer": "10.4.15", "postcss": "8.4.30"}, "devDependencies": {"@types/node": "24.1.0", "@types/react": "19.1.8", "typescript": "^5.8.3"}}
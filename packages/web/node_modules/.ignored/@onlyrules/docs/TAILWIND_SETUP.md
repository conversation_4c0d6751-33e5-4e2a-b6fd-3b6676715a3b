# Tailwind CSS Setup for OnlyRules Documentation

This document explains the Tailwind CSS integration for the OnlyRules documentation package.

## Overview

The documentation system now uses Tailwind CSS for styling, providing a consistent and modern design system that integrates seamlessly with the main web application.

## Architecture

### Package Structure
- `packages/docs/` - Documentation content and configuration
- `packages/web/` - Main web application that renders the docs

### Configuration Files

#### `packages/docs/tailwind.config.ts`
- Tailwind configuration for the docs package
- Extends the main web package configuration
- Includes content paths for docs-specific files

#### `packages/docs/postcss.config.js`
- PostCSS configuration for processing Tailwind CSS
- Enables autoprefixer for cross-browser compatibility

#### `packages/web/app/docs/docs.css`
- Custom CSS styles optimized for Tailwind
- Uses Tailwind utility classes and design tokens
- Provides consistent styling for documentation content

## Features Enabled

### Design System Integration
- Uses the same color palette as the main application
- Consistent typography scale and spacing
- Dark mode support through CSS custom properties

### Tailwind Plugins
- `tailwindcss-animate` - Animation utilities
- `@tailwindcss/typography` - Typography enhancements for prose content

### Responsive Design
- Mobile-first approach
- Container system with responsive padding
- Flexible layout components

## Usage Examples

### Basic Documentation Content
```tsx
// In docs-content.tsx
export const MyDocsPage = () => (
  <div className="prose prose-slate max-w-none dark:prose-invert">
    <h1 className="text-4xl font-bold text-foreground mb-6">Page Title</h1>
    <p className="text-lg text-muted-foreground leading-relaxed mb-8">
      Page description...
    </p>
  </div>
);
```

### Styled Lists
```tsx
<ul className="list-disc list-inside space-y-2 ml-4">
  <li className="text-muted-foreground">
    <strong className="text-foreground">Feature</strong> - Description
  </li>
</ul>
```

### Interactive Links
```tsx
<a 
  href="/docs/guide" 
  className="text-primary hover:text-primary/80 transition-colors underline-offset-4 hover:underline"
>
  Link Text
</a>
```

## Design Tokens

### Colors
- `text-foreground` - Primary text color
- `text-muted-foreground` - Secondary text color
- `text-primary` - Accent/link color
- `bg-card` - Card/container background
- `border-border` - Border color

### Typography
- `text-4xl font-bold` - Main headings
- `text-2xl font-semibold` - Section headings
- `text-lg` - Large body text
- `text-muted-foreground leading-relaxed` - Regular body text

### Spacing
- `space-y-2` - Small vertical spacing between items
- `mb-4`, `mt-8` - Standard margins
- `p-6` - Container padding

## Content Paths

The Tailwind configuration includes the following content paths:
- `packages/docs/lib/**/*.{js,ts,jsx,tsx,mdx}`
- `packages/docs/content/**/*.{js,ts,jsx,tsx,mdx}`
- `packages/web/app/docs/**/*.{js,ts,jsx,tsx,mdx}`
- `packages/web/components/**/*.{js,ts,jsx,tsx,mdx}`

## Development

### Adding New Styles
1. Use existing Tailwind utility classes when possible
2. Follow the established design tokens for consistency
3. Test in both light and dark modes
4. Ensure responsive behavior

### Custom Components
When creating new documentation components, use the established patterns:
- Consistent spacing using Tailwind classes
- Proper semantic HTML structure
- Accessible color contrasts
- Responsive design considerations

## Integration Notes

- The docs package shares the same Tailwind configuration as the web package
- CSS custom properties are used for theme variables
- The setup supports both Radix UI themes and Tailwind utility classes
- All documentation content is processed through the web package's build system
"use client";

import { <PERSON><PERSON>, <PERSON>, Flex, Text, TextField, Theme } from '@radix-ui/themes';
import { Sun, Moon, Search } from 'lucide-react';
import { useState } from 'react';

export function IntegrationTest() {
  const [isDark, setIsDark] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <Theme appearance={isDark ? 'dark' : 'light'}>
      <div className="min-h-screen bg-background p-8">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Header */}
          <Flex justify="between" align="center">
            <Text size="6" className="font-bold">Tailwind + Radix Integration Test</Text>
            <Button
              variant="ghost"
              size="2"
              onClick={() => setIsDark(!isDark)}
              className="hover:bg-accent"
            >
              {isDark ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
            </Button>
          </Flex>

          {/* Color Palette Test */}
          <Card className="p-6">
            <Text size="4" className="font-semibold mb-4">Color System Test</Text>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-primary text-primary-foreground p-4 rounded-md">
                Primary
              </div>
              <div className="bg-secondary text-secondary-foreground p-4 rounded-md">
                Secondary
              </div>
              <div className="bg-accent text-accent-foreground p-4 rounded-md">
                Accent
              </div>
              <div className="bg-destructive text-destructive-foreground p-4 rounded-md">
                Destructive
              </div>
            </div>
          </Card>

          {/* Responsive Layout Test */}
          <Card className="p-6">
            <Text size="4" className="font-semibold mb-4">Responsive Layout</Text>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="bg-card p-4 rounded-lg border">
                <Text className="font-medium">Card 1</Text>
                <Text className="text-sm text-muted-foreground mt-2">
                  Mobile-first responsive design
                </Text>
              </div>
              <div className="bg-card p-4 rounded-lg border">
                <Text className="font-medium">Card 2</Text>
                <Text className="text-sm text-muted-foreground mt-2">
                  Grid layout with Tailwind
                </Text>
              </div>
              <div className="bg-card p-4 rounded-lg border">
                <Text className="font-medium">Card 3</Text>
                <Text className="text-sm text-muted-foreground mt-2">
                  Radix + Tailwind integration
                </Text>
              </div>
            </div>
          </Card>

          {/* Form Components Test */}
          <Card className="p-6">
            <Text size="4" className="font-semibold mb-4">Form Components</Text>
            <Flex direction="column" gap="4">
              <TextField.Root>
                <TextField.Slot>
                  <Search className="h-4 w-4" />
                </TextField.Slot>
                <TextField.Input
                  placeholder="Search with Radix + Tailwind..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="placeholder:text-muted-foreground"
                />
              </TextField.Root>

              <div className="flex flex-wrap gap-2">
                <Button variant="default" size="2">
                  Default Button
                </Button>
                <Button variant="secondary" size="2">
                  Secondary
                </Button>
                <Button variant="outline" size="2">
                  Outline
                </Button>
                <Button variant="ghost" size="2">
                  Ghost
                </Button>
              </div>
            </Flex>
          </Card>

          {/* Typography Test */}
          <Card className="p-6">
            <Text size="4" className="font-semibold mb-4">Typography Scale</Text>
            <div className="space-y-2">
              <Text size="6" className="font-bold">Heading 1 (size 6)</Text>
              <Text size="5" className="font-semibold">Heading 2 (size 5)</Text>
              <Text size="4" className="font-medium">Heading 3 (size 4)</Text>
              <Text size="3">Body text (size 3)</Text>
              <Text size="2" className="text-muted-foreground">
                Small text (size 2)
              </Text>
              <Text size="1" className="text-muted-foreground">
                Extra small text (size 1)
              </Text>
            </div>
          </Card>

          {/* Mobile Responsive Test */}
          <Card className="p-6">
            <Text size="4" className="font-semibold mb-4">Mobile Responsive</Text>
            <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-3 gap-4">
              <div className="bg-blue-100 dark:bg-blue-900 p-4 rounded-md text-center">
                <Text className="text-xs sm:text-sm">Responsive</Text>
              </div>
              <div className="bg-green-100 dark:bg-green-900 p-4 rounded-md text-center">
                <Text className="text-xs sm:text-sm">Design</Text>
              </div>
              <div className="bg-purple-100 dark:bg-purple-900 p-4 rounded-md text-center">
                <Text className="text-xs sm:text-sm">System</Text>
              </div>
            </div>
          </Card>

          {/* Status Badge */}
          <Card className="p-6">
            <Flex justify="between" align="center">
              <Text size="4" className="font-semibold">Integration Status</Text>
              <div className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium">
                ✅ Working Perfectly
              </div>
            </Flex>
            <Text className="text-muted-foreground mt-2">
              Tailwind CSS and Radix UI are now seamlessly integrated!
            </Text>
          </Card>
        </div>
      </div>
    </Theme>
  );
}

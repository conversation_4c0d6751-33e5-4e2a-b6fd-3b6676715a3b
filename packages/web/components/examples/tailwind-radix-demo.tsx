"use client";

import { But<PERSON>, Card, Text, Flex, Box, Badge } from '@radix-ui/themes';

/**
 * Demo component showing how to use Tailwind CSS with Radix UI Themes
 * This demonstrates the resolved conflict between the two systems
 */
export function TailwindRadixDemo() {
  return (
    <div className="p-6 space-y-6">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-foreground mb-4">
          Tailwind CSS + Radix UI Themes Integration
        </h1>
        
        <p className="text-muted-foreground mb-8">
          This demo shows how Tailwind CSS utilities work seamlessly with Radix UI Themes components.
        </p>

        {/* Grid layout using Tailwind */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          
          {/* Card 1: Pure Radix UI */}
          <Card className="p-4">
            <Flex direction="column" gap="3">
              <Text size="4" weight="bold">Pure Radix UI</Text>
              <Text size="2" color="gray">
                This card uses only Radix UI components with their default styling.
              </Text>
              <Button>Radix Button</Button>
            </Flex>
          </Card>

          {/* Card 2: Tailwind + Radix UI */}
          <Card className="p-4 bg-card border border-border">
            <Flex direction="column" gap="3">
              <Text size="4" weight="bold" className="text-foreground">
                Tailwind + Radix UI
              </Text>
              <Text size="2" className="text-muted-foreground">
                This card combines Tailwind utilities with Radix components.
              </Text>
              <Button className="bg-primary text-primary-foreground hover:bg-primary/90">
                Styled Button
              </Button>
            </Flex>
          </Card>

          {/* Card 3: Advanced Integration */}
          <Card className="p-4 bg-gradient-to-br from-primary/10 to-accent/10 border border-border">
            <Flex direction="column" gap="3">
              <div className="flex items-center justify-between">
                <Text size="4" weight="bold" className="text-foreground">
                  Advanced
                </Text>
                <Badge color="blue" className="radix-theme-aware">
                  New
                </Badge>
              </div>
              <Text size="2" className="text-muted-foreground">
                This shows advanced integration with gradients and custom utilities.
              </Text>
              <Button className="bg-accent text-accent-foreground hover:bg-accent/90 transition-colors">
                Gradient Card
              </Button>
            </Flex>
          </Card>
        </div>

        {/* Responsive layout demo */}
        <div className="mt-12 p-6 bg-muted rounded-lg">
          <h2 className="text-2xl font-semibold text-foreground mb-4">
            Responsive Layout Demo
          </h2>
          
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
            <Box className="flex-1">
              <Text className="text-muted-foreground">
                This layout adapts to different screen sizes using Tailwind's responsive utilities
                while maintaining Radix UI's consistent theming.
              </Text>
            </Box>
            
            <div className="flex gap-2 flex-wrap">
              <Button size="2" variant="soft">
                Small
              </Button>
              <Button size="3" variant="solid" className="bg-primary">
                Medium
              </Button>
              <Button size="4" variant="outline" className="border-border">
                Large
              </Button>
            </div>
          </div>
        </div>

        {/* Color system demo */}
        <div className="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="p-4 bg-background border border-border rounded-lg">
            <div className="w-full h-8 bg-primary rounded mb-2"></div>
            <Text size="1" className="text-muted-foreground">Primary</Text>
          </div>
          
          <div className="p-4 bg-background border border-border rounded-lg">
            <div className="w-full h-8 bg-secondary rounded mb-2"></div>
            <Text size="1" className="text-muted-foreground">Secondary</Text>
          </div>
          
          <div className="p-4 bg-background border border-border rounded-lg">
            <div className="w-full h-8 bg-accent rounded mb-2"></div>
            <Text size="1" className="text-muted-foreground">Accent</Text>
          </div>
          
          <div className="p-4 bg-background border border-border rounded-lg">
            <div className="w-full h-8 bg-muted rounded mb-2"></div>
            <Text size="1" className="text-muted-foreground">Muted</Text>
          </div>
        </div>
      </div>
    </div>
  );
}

/* @import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities"; */

/* @import "@onlyrules/docs/dist/index.css"; */
/* @import "@onlyrules/docs/css"; */
@import "./index.css";

/* Radix UI Theme - imported after Tailwind base */
@import "@radix-ui/themes/styles.css";


@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap');



@layer base {
  /* Remove Tailwind's default color variables to avoid conflicts */
  /* Light theme */
  :root {
    /* Background colors */
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    /* Card colors */
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    /* Popover colors */
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    /* Primary colors */
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;

    /* Secondary colors */
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    /* Muted colors */
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    /* Accent colors */
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;

    /* Destructive colors */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    /* Border and input colors */
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 217 91% 60%;

    /* Chart colors */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    /* Border radius */
    --radius: 0.5rem;
  }

  /* Dark theme */
  .dark {
    /* Background colors */
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    /* Card colors */
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    /* Popover colors */
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    /* Primary colors */
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;

    /* Secondary colors */
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    /* Muted colors */
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    /* Accent colors */
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;

    /* Destructive colors */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    /* Border and input colors */
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 217 91% 60%;

    /* Chart colors */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* Responsive typography scale optimized for mobile */
  h1 {
    @apply text-3xl xs:text-4xl md:text-4xl font-bold tracking-tight;
  }

  h2 {
    @apply text-2xl xs:text-2xl md:text-3xl font-semibold tracking-tight;
  }

  h3 {
    @apply text-xl xs:text-xl md:text-2xl font-semibold tracking-tight;
  }

  h4 {
    @apply text-lg xs:text-lg md:text-xl font-semibold tracking-tight;
  }

  h5 {
    @apply text-base xs:text-base md:text-lg font-semibold;
  }

  h6 {
    @apply text-sm xs:text-sm md:text-base font-semibold;
  }

  p {
    @apply leading-6 xs:leading-7 text-sm xs:text-base;
  }

  /* Link styles */
  a {
    @apply transition-colors hover:text-primary;
  }

  /* Code styles */
  code {
    @apply bg-muted px-[0.3rem] py-[0.2rem] rounded text-xs xs:text-sm font-mono;
  }

  pre code {
    @apply bg-transparent p-0;
  }

  /* Scrollbar styles */
  ::-webkit-scrollbar {
    @apply w-1 xs:w-2 h-1 xs:h-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-background;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/20 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/30;
  }

  /* Mobile-specific improvements */
  @media (max-width: 414px) {
    /* Improve touch targets for small screens */
    button, 
    [role="button"],
    input[type="button"],
    input[type="submit"] {
      min-height: 44px; /* iOS recommended minimum touch target */
      min-width: 44px;
    }

    /* Optimize text selection for mobile */
    * {
      -webkit-tap-highlight-color: transparent;
    }
  }
}

@layer utilities {
  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }

  /* Mobile responsive text sizes */
  .text-mobile-responsive {
    @apply text-sm xs:text-base md:text-lg;
  }

  .text-mobile-heading {
    @apply text-lg xs:text-xl md:text-2xl;
  }

  /* Mobile touch targets */
  .touch-target {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }

  /* Mobile padding utilities */
  .mobile-padding {
    @apply px-4 xs:px-6 md:px-8;
  }

  .mobile-margin {
    @apply mx-4 xs:mx-6 md:mx-8;
  }

  /* Animation utilities */
  .animate-in {
    animation: animateIn 0.3s ease-out;
  }

  .animate-out {
    animation: animateOut 0.3s ease-in;
  }

  @keyframes animateIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes animateOut {
    from {
      opacity: 1;
      transform: translateY(0);
    }
    to {
      opacity: 0;
      transform: translateY(10px);
    }
  }

  /* Focus utilities */
  .focus-visible-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background;
  }

  /* Mobile specific utilities */
  .mobile-container {
    @apply container px-4 xs:px-6 md:px-8;
  }

  /* Improved mobile grid layouts */
  .mobile-grid {
    @apply grid grid-cols-1 xs:grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-4 xs:gap-6;
  }

  .mobile-flex {
    @apply flex flex-col xs:flex-col sm:flex-row gap-3 xs:gap-4;
  }

  /* Mobile card improvements */
  .mobile-card {
    @apply p-4 xs:p-5 md:p-6 rounded-lg xs:rounded-xl;
  }
}
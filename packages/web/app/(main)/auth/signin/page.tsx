"use client";

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic';

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import {
  Box,
  Button,
  Card,
  Callout,
  Container,
  Flex,
  Heading,
  Text
} from "@radix-ui/themes";
import { signIn } from "@/lib/auth-client";
import { Code, Github } from "lucide-react";

export default function SignInPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const router = useRouter();

  const handleGitHubSignIn = async () => {
    setIsLoading(true);
    setError("");

    try {
      const result = await signIn.social({
        provider: "github",
        callbackURL: "/dashboard",
      });

      if (result.error) {
        setError(result.error.message || "Failed to sign in with GitHub");
      }
    } catch (err) {
      setError("An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box
      style={{ minHeight: '100vh' }}
      className="flex items-center justify-center py-8"
    >
      <Container size="1" px={{ initial: '4', xs: '6' }}>
        <Flex direction="column" gap={{ initial: '6', xs: '8' }} align="center">
          {/* Logo and Brand */}
          <Box>
            <Link href="/" className="inline-flex items-center gap-2 touch-target">
              <Code className="h-6 w-6 xs:h-8 xs:w-8" />
              <Text size={{ initial: '6', xs: '7' }} weight="bold">
                OnlyRules
              </Text>
            </Link>
          </Box>

          {/* Sign In Card */}
          <Card size="3" style={{ width: '100%', maxWidth: '400px' }}>
            <Flex direction="column" gap="6">
              {/* Header */}
              <Flex direction="column" gap="2" align="center">
                <Heading size={{ initial: '6', xs: '7' }} align="center">
                  Welcome back
                </Heading>
                <Text
                  size={{ initial: '2', xs: '3' }}
                  color="gray"
                  align="center"
                >
                  Sign in with your GitHub account to continue
                </Text>
              </Flex>

              {/* Form Content */}
              <Flex direction="column" gap="4">
                {error && (
                  <Callout.Root color="red" size="1">
                    <Callout.Text size="2">{error}</Callout.Text>
                  </Callout.Root>
                )}

                <Button
                  onClick={handleGitHubSignIn}
                  disabled={isLoading}
                  size="3"
                  style={{ width: '100%' }}
                  className="touch-target"
                >
                  <Github size={18} />
                  {isLoading ? "Signing in..." : "Continue with GitHub"}
                </Button>

                <Text
                  size="1"
                  color="gray"
                  align="center"
                  style={{ lineHeight: '1.5' }}
                >
                  By signing in, you agree to our terms of service and privacy policy.
                </Text>
              </Flex>
            </Flex>
          </Card>
        </Flex>
      </Container>
    </Box>
  );
}
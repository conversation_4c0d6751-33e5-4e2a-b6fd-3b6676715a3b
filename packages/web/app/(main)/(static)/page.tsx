import Link from "next/link";
import { StaticNavbar } from "@/components/layout/static-navbar";

// Force dynamic rendering to avoid build issues
export const dynamic = 'force-dynamic';

export default function HomePage() {
  return (
    <>
      {/* <StaticNavbar /> */}
      <div className="mobile-container py-12 xs:py-16 md:py-20 flex-1 flex items-center">
        <div className="flex flex-col items-center gap-4 xs:gap-6 text-center w-full">
          <h1 className="text-4xl xs:text-5xl md:text-6xl font-bold tracking-tight">
            OnlyRules
          </h1>
          <p className="text-lg xs:text-xl md:text-xl text-muted-foreground max-w-2xl px-4">
            AI Prompt Management Platform
          </p>
          <div className="flex flex-col sm:flex-row gap-3 xs:gap-4 mt-4 xs:mt-6 w-full xs:w-auto">
            <Link
              href="/demo"
              className="inline-block px-4 xs:px-6 py-3 xs:py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm xs:text-base font-medium touch-target text-center"
            >
              View Demo
            </Link>
            <Link
              href="/auth/signin"
              className="inline-block px-4 xs:px-6 py-3 xs:py-3 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors text-sm xs:text-base font-medium touch-target text-center dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
            >
              Get Started
            </Link>
            <Link
              href="/templates"
              className="inline-block px-4 xs:px-6 py-3 xs:py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm xs:text-base font-medium touch-target text-center dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800"
            >
              Browse Templates
            </Link>
          </div>
        </div>
      </div>
    </>
  );
}
